.class public Lcom/snake/helper/TransparentProxyActivity$P2;
.super Lcom/snake/helper/TransparentProxyActivity;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/snake/helper/TransparentProxyActivity;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "P2"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/snake/helper/TransparentProxyActivity;-><init>()V

    return-void
.end method
