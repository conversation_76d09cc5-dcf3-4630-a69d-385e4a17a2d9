.class public Lcom/snake/helper/ProxyJobService$P1;
.super Lcom/snake/helper/ProxyJobService;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/snake/helper/ProxyJobService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "P1"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/snake/helper/ProxyJobService;-><init>()V

    return-void
.end method
