.class public Lcom/snake/helper/ProxyPendingActivity$P3;
.super Lcom/snake/helper/ProxyPendingActivity;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/snake/helper/ProxyPendingActivity;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "P3"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/snake/helper/ProxyPendingActivity;-><init>()V

    return-void
.end method
