.class public abstract Lcom/snake/helper/TransparentProxyActivity;
.super Lcom/snake/helper/ProxyActivity;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/snake/helper/TransparentProxyActivity$P3;,
        Lcom/snake/helper/TransparentProxyActivity$P2;,
        Lcom/snake/helper/TransparentProxyActivity$P1;,
        Lcom/snake/helper/TransparentProxyActivity$P0;
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/snake/helper/ProxyActivity;-><init>()V

    return-void
.end method
