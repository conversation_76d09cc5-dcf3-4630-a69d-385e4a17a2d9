.class public Lcom/snake/helper/ProxyService$P3;
.super Lcom/snake/helper/ProxyService;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/snake/helper/ProxyService;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "P3"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/snake/helper/ProxyService;-><init>()V

    return-void
.end method
