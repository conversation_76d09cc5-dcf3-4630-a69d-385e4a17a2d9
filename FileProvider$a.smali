.class public interface abstract Lcom/snake/helper/FileProvider$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/snake/helper/FileProvider;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract a(Landroid/net/Uri;)Ljava/io/File;
.end method

.method public abstract b(Ljava/io/File;)Landroid/net/Uri;
.end method
